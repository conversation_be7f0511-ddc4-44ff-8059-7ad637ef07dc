// Queue Manager - Manages the scraping queue and processes URLs
require('dotenv').config();
const { db, scrapingQueue, discoveredUrls, properties } = require('../drizzle_client');
const { BrightDataScraper } = require('./bright_data_scraper');
const { WebsiteParserManager } = require('./website_parsers/parser_manager');
const { getCurrencyService } = require('./currency_service');
const { eq, and, lte, desc, asc, count, sql } = require('drizzle-orm');
const OpenAI = require('openai');

// Initialize OpenAI client for embeddings
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

class QueueManager {
  constructor() {
    this.isProcessing = false;
    this.maxConcurrentJobs = 20; // INCREASED: Process 20 URLs at a time for higher throughput
    this.processingInterval = 15000; // REDUCED: Check queue every 15 seconds
    this.brightDataScraper = new BrightDataScraper();
    this.parserManager = new WebsiteParserManager();
    this.parsers = this.parserManager.parsers; // Direct access to parsers
    this.currencyService = getCurrencyService();

    console.log('📋 Queue Manager initialized with HIGH-THROUGHPUT settings');
    console.log(`🚀 Max Concurrent Jobs: ${this.maxConcurrentJobs}`);
    console.log(`⏱️  Processing Interval: ${this.processingInterval/1000}s`);
    console.log(`🧠 Website-specific parsers available: ${Object.keys(this.parsers).join(', ')}`);
    console.log('💱 Currency conversion service initialized');
  }

  // Start the queue processor
  start() {
    console.log('🚀 Starting queue processor...');
    this.processQueue();
    
    // Set up periodic processing
    this.intervalId = setInterval(() => {
      if (!this.isProcessing) {
        this.processQueue();
      }
    }, this.processingInterval);
  }

  // Stop the queue processor
  stop() {
    console.log('⏹️  Stopping queue processor...');
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  // Process ALL pending items in the queue
  async processAllQueue() {
    if (this.isProcessing) {
      console.log('⏳ Queue processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;
    console.log('📋 Processing ALL items in scraping queue...');

    try {
      let totalProcessed = 0;
      let hasMore = true;

      while (hasMore) {
        // Get pending items from queue (ordered by priority and scheduled time)
        const pendingItems = await db.select({
          id: scrapingQueue.id,
          url: scrapingQueue.url,
          website_id: scrapingQueue.website_id,
          priority: scrapingQueue.priority,
          attempts: scrapingQueue.attempts,
          max_attempts: scrapingQueue.max_attempts,
          discovered_url_id: scrapingQueue.discovered_url_id
        })
        .from(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'pending'),
            lte(scrapingQueue.scheduled_for, new Date())
          )
        )
        .orderBy(desc(scrapingQueue.priority), asc(scrapingQueue.scheduled_for))
        .limit(this.maxConcurrentJobs);

        if (pendingItems.length === 0) {
          console.log('📭 No more pending items in queue');
          hasMore = false;
          break;
        }

        console.log(`📦 Processing batch ${Math.floor(totalProcessed / this.maxConcurrentJobs) + 1}: ${pendingItems.length} items`);

        // Group by website for batch processing
        const groupedByWebsite = {};
        pendingItems.forEach(item => {
          if (!groupedByWebsite[item.website_id]) {
            groupedByWebsite[item.website_id] = [];
          }
          groupedByWebsite[item.website_id].push(item);
        });

        // Process each website group
        for (const [websiteId, items] of Object.entries(groupedByWebsite)) {
          await this.processWebsiteGroup(websiteId, items);
        }

        totalProcessed += pendingItems.length;
        console.log(`✅ Processed ${totalProcessed} items so far...`);

        // Small delay between batches to respect rate limits
        if (hasMore) {
          console.log('⏳ Waiting 30 seconds before next batch...');
          await new Promise(resolve => setTimeout(resolve, 30000));
        }
      }

      console.log(`🎉 Completed processing all queue items! Total: ${totalProcessed}`);

    } catch (error) {
      console.error('❌ Error processing all queue:', error.message);
    } finally {
      this.isProcessing = false;
    }
  }

  // Process pending items in the queue (single batch)
  async processQueue(websiteId = null, limit = null) {
    if (this.isProcessing) {
      console.log('⏳ Queue processing already in progress, skipping...');
      return { processed: 0, successful: 0, failed: 0, skipped: 0 };
    }

    this.isProcessing = true;
    console.log('📋 Processing scraping queue...');

    let stats = { processed: 0, successful: 0, failed: 0, skipped: 0 };

    try {
      console.log('🔍 Building query conditions...');

      // Build query conditions
      let conditions = [
        eq(scrapingQueue.status, 'pending'),
        lte(scrapingQueue.scheduled_for, new Date())
      ];

      // Add website filter if specified
      if (websiteId) {
        console.log(`🎯 Filtering by website: ${websiteId}`);
        conditions.push(eq(scrapingQueue.website_id, websiteId));
      }

      console.log('📊 Querying database for pending items...');

      // Get pending items from queue (ordered by priority and scheduled time)
      const pendingItems = await db.select()
        .from(scrapingQueue)
        .where(and(...conditions))
        .orderBy(asc(scrapingQueue.priority), asc(scrapingQueue.scheduled_for))
        .limit(limit || this.maxConcurrentJobs);

      console.log(`📋 Found ${pendingItems.length} pending items`);

      if (pendingItems.length === 0) {
        console.log('📭 No pending items found in queue');
        return stats;
      }

      stats.processed = pendingItems.length;

      // Process each item individually for now (simplified)
      for (const item of pendingItems) {
        try {
          console.log(`🌐 Processing URL: ${item.url}`);

          // Scrape single URL using Bright Data
          const results = await this.brightDataScraper.scrapeUrls([item.url]);
          const result = results[0];

          const success = await this.processQueueItemResult(item, result);
          if (success) {
            stats.successful++;
          } else {
            stats.failed++;
          }
        } catch (error) {
          console.error(`❌ Failed to process ${item.url}:`, error.message);
          stats.failed++;
          await this.markItemFailed(item, error.message);
        }
      }

      return stats;

    } catch (error) {
      console.error('❌ Error processing queue:', error.message);
      console.error('❌ Error stack:', error.stack);
      return stats;
    } finally {
      this.isProcessing = false;
    }
  }

  // Process the result of a single queue item
  async processQueueItemResult(queueItem, scrapingResult) {
    const now = new Date();

    try {
      if (scrapingResult && scrapingResult.success) {
        // Successful scraping with Bright Data
        console.log(`✅ Successfully scraped: ${queueItem.url}`);

        // Parse the scraped content
        const parsedData = await this.parseScrapedContent(queueItem, scrapingResult);

        if (parsedData) {
          // Mark as processed
          await this.markItemProcessed(queueItem);
          return true;
        } else {
          // Mark as failed
          await this.markItemFailed(queueItem, 'Failed to parse content');
          return false;
        }
      } else {
        // Failed scraping
        console.log(`❌ Failed to scrape: ${queueItem.url}`);
        await this.markItemFailed(queueItem, scrapingResult?.error || 'Unknown scraping error');
        return false;
      }
    } catch (error) {
      console.error(`❌ Error processing queue item result: ${error.message}`);
      await this.markItemFailed(queueItem, error.message);
      return false;
    }
  }

  // Mark item as processed
  async markItemProcessed(queueItem) {
    try {
      await db.update(scrapingQueue)
        .set({
          status: 'completed',
          processed_at: new Date()
        })
        .where(eq(scrapingQueue.id, queueItem.id));
    } catch (error) {
      console.error(`❌ Failed to mark item as processed: ${error.message}`);
    }
  }

  // Mark item as failed
  async markItemFailed(queueItem, errorMessage) {
    try {
      await db.update(scrapingQueue)
        .set({
          status: 'failed',
          processed_at: new Date(),
          attempts: (queueItem.attempts || 0) + 1
        })
        .where(eq(scrapingQueue.id, queueItem.id));
    } catch (error) {
      console.error(`❌ Failed to mark item as failed: ${error.message}`);
    }
  }

  // Process a group of URLs from the same website
  async processWebsiteGroup(websiteId, items) {
    console.log(`🌐 Processing ${items.length} URLs for ${websiteId}`);

    let stats = { successful: 0, failed: 0, skipped: 0 };

    // Mark items as processing
    const itemIds = items.map(item => item.id);
    await db.update(scrapingQueue)
      .set({
        status: 'processing',
        started_at: new Date(),
        updated_at: new Date()
      })
      .where(eq(scrapingQueue.id, itemIds[0])); // Update first item as example

    try {
      // Extract URLs for batch processing
      const urls = items.map(item => item.url);

      // Run batch extraction with Bright Data
      console.log(`🚀 Processing ${urls.length} URLs with Bright Data for ${websiteId}`);
      const results = await this.brightDataScraper.processBatch(urls, {
        batchSize: 3, // Smaller batches for better control
        delay: 2000   // 2 seconds between batches
      });

      // Process results
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const result = results[i];

        const success = await this.processQueueItemResult(item, result);
        if (success) {
          stats.successful++;
        } else {
          stats.failed++;
        }
      }

      console.log(`✅ Completed processing ${items.length} URLs for ${websiteId}`);
      return stats;

    } catch (error) {
      console.error(`❌ Failed to process ${websiteId} group:`, error.message);
      stats.failed += items.length;
      return stats;
      
      // Mark all items as failed
      for (const item of items) {
        await this.markItemFailed(item, error.message);
      }
    }
  }

  // Parse scraped content using website-specific parsers (DIRECT - NO AI PARSER)
  async parseScrapedContent(queueItem, scrapedData) {
    try {
      console.log(`🧠 [Queue Manager] Parsing content for ${queueItem.url} using DIRECT website-specific parsers (NO AI)`);

      // Get HTML content from Bright Data
      const htmlContent = scrapedData.body || scrapedData.content || scrapedData.html || '';

      if (!htmlContent) {
        console.log(`⚠️  [Queue Manager] No HTML content found for ${queueItem.url}`);
        return null;
      }

      // Get website type from database queue item (much better than URL parsing!)
      const websiteId = queueItem.website_id;
      console.log(`🌐 [Queue Manager] Using database website_id: ${websiteId} for ${queueItem.url}`);

      // DIRECTLY use the old parsers (SKIP Bright Data AI Parser completely)
      let rawPropertyData = null;

      if (websiteId === 'bali_home_immo') {
        // Use Bali Home Immo parser DIRECTLY
        console.log(`🔧 [Queue Manager] Using DIRECT Bali Home Immo parser`);
        const parser = this.parsers.bali_home_immo;
        rawPropertyData = parser.parsePropertyData(htmlContent, queueItem.url);
      } else if (websiteId === 'betterplace') {
        // Use BetterPlace parser DIRECTLY
        console.log(`🔧 [Queue Manager] Using DIRECT BetterPlace parser`);
        const parser = this.parsers.betterplace;
        rawPropertyData = parser.parsePropertyData(htmlContent, queueItem.url);
      } else if (websiteId === 'bali_villa_realty') {
        // Use Bali Villa Realty parser DIRECTLY
        console.log(`🔧 [Queue Manager] Using DIRECT Bali Villa Realty parser`);
        const parser = this.parsers.bali_villa_realty;
        rawPropertyData = parser.parsePropertyData(htmlContent, queueItem.url);
      } else {
        console.log(`❌ [Queue Manager] No parser found for website: ${websiteId}`);
        return null;
      }

      if (!rawPropertyData) {
        console.log(`❌ [Queue Manager] Failed to extract property data from ${queueItem.url}`);
        return null;
      }

      // Skip properties with no price (Price on Request)
      if (!rawPropertyData.price || rawPropertyData.price === null) {
        console.log(`🚫 [Queue Manager] Skipping property with no price (Price on Request): ${queueItem.url}`);
        return null;
      }

      console.log(`✅ [Queue Manager] Successfully parsed ${queueItem.url} with DIRECT ${websiteId} parser`);
      console.log(`📊 [Queue Manager] Raw data:`, JSON.stringify(rawPropertyData, null, 2));

      // Use the old mappers for consistent data mapping
      const { mapBaliHomeImmo, mapBetterPlace, mapBaliVillaRealty } = require('./mappers');
      let mappedData = null;

      if (websiteId === 'bali_home_immo') {
        console.log(`🗺️  [Queue Manager] Using mapBaliHomeImmo mapper`);
        mappedData = mapBaliHomeImmo(rawPropertyData);
      } else if (websiteId === 'betterplace') {
        console.log(`🗺️  [Queue Manager] Using mapBetterPlace mapper (async)`);
        mappedData = await mapBetterPlace(rawPropertyData); // BetterPlace mapper is async for currency conversion
      } else if (websiteId === 'bali_villa_realty') {
        console.log(`🗺️  [Queue Manager] Using mapBaliVillaRealty mapper (async)`);
        mappedData = await mapBaliVillaRealty(rawPropertyData); // Bali Villa Realty mapper is async for currency conversion
      }

      if (!mappedData) {
        console.log(`❌ [Queue Manager] Failed to map property data from ${queueItem.url}`);
        return null;
      }

      console.log(`✅ [Queue Manager] Successfully mapped data for ${queueItem.url}`);
      console.log(`📊 [Queue Manager] Mapped data:`, JSON.stringify(mappedData, null, 2));

      // The mappers already handle currency conversion, so we can use the mapped data directly
      // Insert property into database with mapped data
      const propertyToInsert = {
        title: mappedData.title || 'Untitled Property',
        category: mappedData.category || 'RESIDENTIAL',
        type: mappedData.type || 'VILLA',
        status: mappedData.status || 'AVAILABLE',
        address: mappedData.address || 'Bali, Indonesia',
        city: mappedData.city || 'Bali',
        state: mappedData.state || 'Bali',
        country: mappedData.country || 'Indonesia',
        price: mappedData.price || null,
        rent_price: mappedData.rent_price || null,
        bedrooms: mappedData.bedrooms || null,
        bathrooms: mappedData.bathrooms || null,
        parking_spaces: mappedData.parking_spaces || null,
        size_sqft: mappedData.size_sqft || null,
        lot_size_sqft: mappedData.lot_size_sqft || null,
        year_built: mappedData.year_built || null,
        amenities: mappedData.amenities || null,
        media: mappedData.media || null,
        source_id: queueItem.website_id,
        external_id: mappedData.media?.external_id || null,
        source_url: queueItem.url
      };

      console.log(`💾 [Queue Manager] Inserting property into database: ${mappedData.title || 'No title'}`);
      console.log(`📊 [Queue Manager] Property to insert:`, JSON.stringify(propertyToInsert, null, 2));

      const insertedProperty = await db.insert(properties).values(propertyToInsert).returning();

      if (insertedProperty && insertedProperty[0]) {
        console.log(`✅ [Queue Manager] Property inserted with ID: ${insertedProperty[0].id}`);

        // Generate and save vector embedding
        const propertyId = insertedProperty[0].id;
        try {
          const descriptionText = this.generateDescriptionText(mappedData, rawPropertyData);
          const embedding = await this.generateOpenAIEmbedding(descriptionText);

          if (embedding) {
            await db.execute(sql`
              UPDATE property
              SET vector = ${JSON.stringify(embedding)}::vector
              WHERE id = ${propertyId}
            `);
            console.log(`🔍 [Queue Manager] Vector embedding generated for: ${mappedData.title}`);
          }
        } catch (vectorError) {
          console.log(`⚠️ [Queue Manager] Vector generation failed for ${mappedData.title}: ${vectorError.message}`);
        }

        return insertedProperty[0];
      } else {
        console.log(`❌ [Queue Manager] Failed to insert property into database`);
        return null;
      }

    } catch (error) {
      console.log(`❌ Failed to parse content for ${queueItem.url}: ${error.message}`);
      return null;
    }
  }

  // Process the result of a single queue item
  async processQueueItemResult(queueItem, scrapingResult) {
    const now = new Date();

    try {
      if (scrapingResult && scrapingResult.success) {
        // Successful scraping with Bright Data
        console.log(`✅ Successfully scraped: ${queueItem.url}`);

        // Parse the scraped content using existing parsers
        const parseResult = await this.parseScrapedContent(queueItem, scrapingResult.data);

        // Update queue item as completed
        await db.update(scrapingQueue)
          .set({
            status: 'completed',
            completed_at: now,
            updated_at: now
          })
          .where(eq(scrapingQueue.id, queueItem.id));

        // Update discovered URL
        await db.update(discoveredUrls)
          .set({
            last_scraped_at: now,
            scrape_status: 'scraped',
            property_id: parseResult?.id || null, // Link to the created property
            updated_at: now
          })
          .where(eq(discoveredUrls.id, queueItem.discovered_url_id));

        // Update property with source URL reference
        if (parseResult?.id) {
          await db.update(properties)
            .set({
              source_url_id: queueItem.discovered_url_id,
              last_scraped_at: now
            })
            .where(eq(properties.id, parseResult.id));
        }

        return true; // Success

      } else {
        // Failed scraping
        const errorMessage = scrapingResult?.error || 'Unknown scraping error';
        console.log(`❌ Failed to scrape: ${queueItem.url} - ${errorMessage}`);

        await this.markItemFailed(queueItem, errorMessage);
        return false; // Failed
      }

    } catch (error) {
      console.error(`❌ Error processing result for ${queueItem.url}:`, error.message);
      await this.markItemFailed(queueItem, error.message);
      return false; // Failed
    }
  }

  // Mark a queue item as failed
  async markItemFailed(queueItem, errorMessage) {
    const now = new Date();
    const newAttempts = queueItem.attempts + 1;

    if (newAttempts >= queueItem.max_attempts) {
      // Max attempts reached, mark as failed
      await db.update(scrapingQueue)
        .set({
          status: 'failed',
          attempts: newAttempts,
          error_message: errorMessage,
          completed_at: now,
          updated_at: now
        })
        .where(eq(scrapingQueue.id, queueItem.id));

      // Update discovered URL
      await db.update(discoveredUrls)
        .set({
          scrape_status: 'failed',
          scrape_attempts: newAttempts,
          updated_at: now
        })
        .where(eq(discoveredUrls.id, queueItem.discovered_url_id));

    } else {
      // Retry later
      const retryDelay = Math.pow(2, newAttempts) * 60 * 1000; // Exponential backoff
      const nextAttempt = new Date(now.getTime() + retryDelay);

      await db.update(scrapingQueue)
        .set({
          status: 'pending',
          attempts: newAttempts,
          error_message: errorMessage,
          scheduled_for: nextAttempt,
          started_at: null,
          updated_at: now
        })
        .where(eq(scrapingQueue.id, queueItem.id));

      console.log(`🔄 Scheduled retry for ${queueItem.url} in ${Math.round(retryDelay / 60000)} minutes`);
    }
  }

  // Get queue statistics
  async getQueueStats() {
    try {
      const stats = await db.select({
        status: scrapingQueue.status,
        count: count()
      })
      .from(scrapingQueue)
      .groupBy(scrapingQueue.status);

      const result = {
        total: 0,
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0
      };

      stats.forEach(stat => {
        result[stat.status] = parseInt(stat.count);
        result.total += parseInt(stat.count);
      });

      return result;
    } catch (error) {
      console.error('❌ Error getting queue stats:', error.message);
      return null;
    }
  }

  // Add URL to queue manually
  async addToQueue(url, websiteId, priority = 5) {
    try {
      // Check if URL already exists in discovered_urls
      let discoveredUrl = await db.select().from(discoveredUrls)
        .where(eq(discoveredUrls.url, url));

      if (discoveredUrl.length === 0) {
        // Create discovered URL entry
        discoveredUrl = await db.insert(discoveredUrls).values({
          url: url,
          website_id: websiteId,
          url_type: 'property',
          is_property_page: true,
          confidence_score: 1.0,
          classification_reason: 'Manually added',
          discovered_at: new Date()
        }).returning();
      }

      // Add to queue
      await db.insert(scrapingQueue).values({
        discovered_url_id: discoveredUrl[0].id,
        url: url,
        website_id: websiteId,
        priority: priority,
        scheduled_for: new Date()
      });

      console.log(`➕ Added ${url} to scraping queue`);
      return true;

    } catch (error) {
      console.error(`❌ Failed to add ${url} to queue:`, error.message);
      return false;
    }
  }

  // Clear completed items from queue (cleanup)
  async cleanupQueue(olderThanDays = 7) {
    try {
      const cutoffDate = new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000));
      
      const result = await db.delete(scrapingQueue)
        .where(
          and(
            eq(scrapingQueue.status, 'completed'),
            lte(scrapingQueue.completed_at, cutoffDate)
          )
        );

      console.log(`🧹 Cleaned up queue: removed ${result.rowCount || 0} completed items older than ${olderThanDays} days`);
      return result.rowCount || 0;

    } catch (error) {
      console.error('❌ Error cleaning up queue:', error.message);
      return 0;
    }
  }

  // Helper function to determine website ID from URL
  getWebsiteIdFromUrl(url) {
    if (url.includes('bali-home-immo.com')) {
      return 'bali_home_immo';
    } else if (url.includes('betterplace.cc') || url.includes('betterplace.co.id')) {
      return 'betterplace';
    } else if (url.includes('balivillarealty.com')) {
      return 'bali_villa_realty';
    } else {
      return 'unknown';
    }
  }

  // Generate description text for embedding
  generateDescriptionText(mappedProperty, rawData) {
    const parts = [];

    if (mappedProperty.title) parts.push(`Title: ${mappedProperty.title}`);
    if (mappedProperty.address) parts.push(`Location: ${mappedProperty.address}`);
    if (mappedProperty.bedrooms) parts.push(`${mappedProperty.bedrooms} bedrooms`);
    if (mappedProperty.bathrooms) parts.push(`${mappedProperty.bathrooms} bathrooms`);
    if (mappedProperty.parking_spaces) parts.push(`${mappedProperty.parking_spaces} parking spaces`);
    if (mappedProperty.size_sqft) parts.push(`${Math.round(mappedProperty.size_sqft)} sqft building`);
    if (mappedProperty.lot_size_sqft) parts.push(`${Math.round(mappedProperty.lot_size_sqft)} sqft lot`);
    if (mappedProperty.year_built) parts.push(`Built in ${mappedProperty.year_built}`);

    // Add price information
    if (mappedProperty.price) parts.push(`Sale price: IDR ${mappedProperty.price.toLocaleString()}`);
    if (mappedProperty.rent_price) parts.push(`Rent: IDR ${mappedProperty.rent_price.toLocaleString()}/month`);

    // Add amenities
    if (mappedProperty.amenities?.raw_amenities?.length > 0) {
      parts.push(`Amenities: ${mappedProperty.amenities.raw_amenities.join(', ')}`);
    }

    // Add description if available
    if (rawData?.description) {
      parts.push(`Description: ${rawData.description}`);
    }

    return parts.join('. ');
  }

  // Generate OpenAI embedding
  async generateOpenAIEmbedding(text) {
    try {
      const response = await openai.embeddings.create({
        model: "text-embedding-3-small",
        input: text,
        encoding_format: "float",
      });

      return response.data[0].embedding;
    } catch (error) {
      console.log(`❌ OpenAI embedding failed: ${error.message}`);
      return null;
    }
  }
}

module.exports = { QueueManager };
