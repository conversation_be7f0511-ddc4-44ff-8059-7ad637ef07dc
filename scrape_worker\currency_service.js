// Currency Service - Handles currency conversion
require('dotenv').config();
const { db, exchangeRates } = require('../drizzle_client');
const { desc, eq } = require('drizzle-orm');

class CurrencyService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours
  }

  // Get exchange rate from database or API
  async getExchangeRate(fromCurrency, toCurrency) {
    if (fromCurrency === toCurrency) return 1;

    const cacheKey = `${fromCurrency}_${toCurrency}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.cacheExpiry) {
      return cached.rate;
    }

    try {
      // Try to get from database first
      const dbRate = await db.select()
        .from(exchangeRates)
        .where(eq(exchangeRates.from_currency, fromCurrency))
        .where(eq(exchangeRates.to_currency, toCurrency))
        .orderBy(desc(exchangeRates.date))
        .limit(1);

      if (dbRate.length > 0) {
        const rate = parseFloat(dbRate[0].rate);
        this.cache.set(cacheKey, { rate, timestamp: Date.now() });
        return rate;
      }

      // Fallback to hardcoded rates for common conversions
      const hardcodedRates = {
        'USD_IDR': 15500,
        'EUR_IDR': 17000,
        'SGD_IDR': 11500,
        'AUD_IDR': 10500
      };

      const rate = hardcodedRates[cacheKey] || 1;
      this.cache.set(cacheKey, { rate, timestamp: Date.now() });
      return rate;

    } catch (error) {
      console.log(`❌ Currency service error: ${error.message}`);
      return 1; // Fallback to 1:1 rate
    }
  }

  // Convert price to IDR
  async convertToIDR(amount, fromCurrency) {
    if (!amount || fromCurrency === 'IDR') return amount;
    
    const rate = await this.getExchangeRate(fromCurrency, 'IDR');
    return Math.round(amount * rate);
  }
}

let currencyServiceInstance = null;

function getCurrencyService() {
  if (!currencyServiceInstance) {
    currencyServiceInstance = new CurrencyService();
  }
  return currencyServiceInstance;
}

module.exports = { CurrencyService, getCurrencyService };
