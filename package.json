{"dependencies": {"@prisma/client": "^6.13.0", "cors": "^2.8.5", "dotenv": "^17.2.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.4", "express": "^5.1.0", "fast-xml-parser": "^5.2.5", "firecrawl": "^1.29.3", "helmet": "^8.1.0", "morgan": "^1.10.1", "node-fetch": "^2.7.0", "openai": "^5.12.2", "pg": "^8.16.3", "postgres": "^3.4.7", "postgres-js": "^0.1.0", "prisma": "^6.13.0"}, "devDependencies": {"@types/node": "^24.2.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}