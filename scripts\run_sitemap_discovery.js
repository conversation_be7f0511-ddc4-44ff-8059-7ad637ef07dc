#!/usr/bin/env node
// <PERSON>ript to run sitemap discovery and populate the queue
require('dotenv').config();
const { QueueManager } = require('../scrape_worker/queue_manager');

async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  const websiteId = args[1];

  const queueManager = new QueueManager();

  try {
    switch (command) {
      case 'all':
        console.log('🚀 Running sitemap discovery for all websites...');
        await queueManager.discoverFromAllSitemaps();
        break;

      case 'website':
        if (!websiteId) {
          console.error('❌ Please specify a website ID');
          console.log('Available websites: bali_home_immo, bali_villa_realty, betterplace');
          process.exit(1);
        }
        console.log(`🚀 Running sitemap discovery for ${websiteId}...`);
        await queueManager.discoverFromSitemap(websiteId);
        break;

      case 'test':
        console.log('🧪 Testing sitemap discovery (dry run)...');
        const testWebsite = websiteId || 'betterplace';
        const discovery = queueManager.sitemapDiscovery;
        const discovered = await discovery.discoverUrlsFromSitemaps(testWebsite);
        
        console.log(`\n📊 Test Results for ${testWebsite}:`);
        console.log(`   Property URLs found: ${discovered.propertyUrls.length}`);
        console.log(`   Listing URLs found: ${discovered.listingUrls.length}`);
        
        if (discovered.propertyUrls.length > 0) {
          console.log('\n🔍 Sample Property URLs:');
          discovered.propertyUrls.slice(0, 5).forEach((url, i) => {
            console.log(`   ${i + 1}. ${url.url}`);
          });
        }
        break;

      default:
        console.log('🗺️  Sitemap Discovery Tool');
        console.log('');
        console.log('Commands:');
        console.log('  all                    - Run sitemap discovery for all websites');
        console.log('  website <website_id>   - Run sitemap discovery for specific website');
        console.log('  test [website_id]      - Test sitemap discovery (dry run, no queue updates)');
        console.log('');
        console.log('Available websites:');
        console.log('  - bali_home_immo');
        console.log('  - bali_villa_realty');
        console.log('  - betterplace');
        console.log('');
        console.log('Examples:');
        console.log('  node scripts/run_sitemap_discovery.js all');
        console.log('  node scripts/run_sitemap_discovery.js website betterplace');
        console.log('  node scripts/run_sitemap_discovery.js test bali_home_immo');
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error.stack);
    process.exit(1);
  }

  console.log('\n✅ Sitemap discovery completed!');
  process.exit(0);
}

if (require.main === module) {
  main();
}

module.exports = { main };
