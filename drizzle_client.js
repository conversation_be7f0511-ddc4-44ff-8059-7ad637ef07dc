// Drizzle Database Client
require('dotenv').config();
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { 
  properties, 
  exchangeRates, 
  sources, 
  scrapingQueue, 
  discoveredUrls 
} = require('./drizzle/schema');

// Database connection
const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  throw new Error('DATABASE_URL environment variable is required');
}

const sql = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
});

const db = drizzle(sql);

// Close connection function
async function closeConnection() {
  await sql.end();
}

module.exports = {
  db,
  sql,
  closeConnection,
  // Export schema tables
  properties,
  exchangeRates,
  sources,
  scrapingQueue,
  discoveredUrls
};
