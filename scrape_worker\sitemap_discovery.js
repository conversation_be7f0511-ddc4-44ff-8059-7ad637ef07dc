// Modern Sitemap-based URL Discovery System
// Integrated with the modern queue manager
require('dotenv').config();
const { db, discoveredUrls, scrapingQueue } = require('../drizzle_client');
const { XMLParser } = require('fast-xml-parser');
const { eq, and } = require('drizzle-orm');

class SitemapDiscovery {
  constructor() {
    // Updated sitemap URLs with corrected betterplace URL
    this.sitemapUrls = {
      bali_home_immo: [
        'https://bali-home-immo.com/sitemap.xml'
      ],
      bali_villa_realty: [
        'https://balivillarealty.com/property-sitemap.xml',
        'https://balivillarealty.com/property-sitemap2.xml'
      ],
      betterplace: [
        'https://betterplace.cc/sitemap_index.xml'  // Corrected from httos to https
      ]
    };
    
    this.xmlParser = new XMLParser({
      ignoreAttributes: false,
      attributeNamePrefix: '@_'
    });
  }

  // Fetch and parse sitemap XML
  async fetchSitemap(sitemapUrl) {
    console.log(`🔍 Fetching sitemap: ${sitemapUrl}`);
    
    try {
      const response = await fetch(sitemapUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; PropertyBot/1.0)'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const xmlContent = await response.text();
      const parsed = this.xmlParser.parse(xmlContent);
      
      console.log(`✅ Successfully parsed sitemap: ${sitemapUrl}`);
      return parsed;
      
    } catch (error) {
      console.log(`❌ Error fetching sitemap ${sitemapUrl}: ${error.message}`);
      return null;
    }
  }

  // Extract URLs from sitemap data
  extractUrlsFromSitemap(sitemapData) {
    const urls = [];
    
    if (!sitemapData) return urls;

    // Handle sitemap index (contains links to other sitemaps)
    if (sitemapData.sitemapindex && sitemapData.sitemapindex.sitemap) {
      const sitemaps = Array.isArray(sitemapData.sitemapindex.sitemap) 
        ? sitemapData.sitemapindex.sitemap 
        : [sitemapData.sitemapindex.sitemap];
      
      sitemaps.forEach(sitemap => {
        if (sitemap.loc) {
          urls.push({
            url: sitemap.loc,
            type: 'sitemap',
            lastmod: sitemap.lastmod || null
          });
        }
      });
    }

    // Handle URL set (actual page URLs)
    if (sitemapData.urlset && sitemapData.urlset.url) {
      const urlEntries = Array.isArray(sitemapData.urlset.url) 
        ? sitemapData.urlset.url 
        : [sitemapData.urlset.url];
      
      urlEntries.forEach(urlEntry => {
        if (urlEntry.loc) {
          urls.push({
            url: urlEntry.loc,
            type: 'page',
            lastmod: urlEntry.lastmod || null,
            changefreq: urlEntry.changefreq || null,
            priority: urlEntry.priority || null
          });
        }
      });
    }

    return urls;
  }

  // Filter URLs to find property pages
  filterPropertyUrls(urls, websiteId) {
    console.log(`🔍 Filtering ${urls.length} URLs for ${websiteId} property pages...`);
    
    const propertyUrls = [];
    const listingUrls = [];
    
    urls.forEach(urlData => {
      const url = urlData.url;
      
      if (websiteId === 'bali_home_immo') {
        if (this.isBaliHomeImmoPropertyUrl(url)) {
          propertyUrls.push({
            ...urlData,
            website_id: websiteId,
            confidence: 0.9
          });
        } else if (this.isBaliHomeImmoListingUrl(url)) {
          listingUrls.push({
            ...urlData,
            website_id: websiteId,
            confidence: 0.8
          });
        }
      }
      
      if (websiteId === 'bali_villa_realty') {
        if (this.isBaliVillaRealtyPropertyUrl(url)) {
          propertyUrls.push({
            ...urlData,
            website_id: websiteId,
            confidence: 0.9
          });
        }
      }
      
      if (websiteId === 'betterplace') {
        if (this.isBetterPlacePropertyUrl(url)) {
          propertyUrls.push({
            ...urlData,
            website_id: websiteId,
            confidence: 0.9
          });
        }
      }
    });
    
    console.log(`✅ Found ${propertyUrls.length} property URLs and ${listingUrls.length} listing URLs`);
    return { propertyUrls, listingUrls };
  }

  // Check if URL is a Bali Home Immo property page
  isBaliHomeImmoPropertyUrl(url) {
    const propertyPatterns = [
      /\/realestate-property\/[^\/]+\/[^\/]+\/[^\/]+\/[^\/]+\/[^\/]+$/i,
      /\/property\/\d+/i,
      /\/villa\/[^\/]+-[^\/]+$/i,
      /-rf\d+/i,
      /-bhi\d+/i,
      /-cb\d+/i
    ];
    
    return propertyPatterns.some(pattern => pattern.test(url)) && 
           !this.isBaliHomeImmoListingUrl(url);
  }

  // Check if URL is a Bali Home Immo listing page
  isBaliHomeImmoListingUrl(url) {
    const listingPatterns = [
      /\/realestate-property\/[^\/]+\/[^\/]+\/[^\/]+\/?$/i,
      /\/for-(rent|sale)\/?$/i,
      /\/villa\/?$/i,
      /\/apartment\/?$/i,
      /\/monthly\/?$/i,
      /\/yearly\/?$/i,
      /\/freehold\/?$/i,
      /\/leasehold\/?$/i
    ];
    
    return listingPatterns.some(pattern => pattern.test(url));
  }

  // Check if URL is a Bali Villa Realty property page
  isBaliVillaRealtyPropertyUrl(url) {
    const propertyPatterns = [
      /\/property\/[^\/]+$/i,
      /\/villa\/[^\/]+$/i,
      /\/apartment\/[^\/]+$/i,
      /\/\d+/i
    ];
    
    return propertyPatterns.some(pattern => pattern.test(url));
  }

  // Check if URL is a BetterPlace property page
  isBetterPlacePropertyUrl(url) {
    const propertyPatterns = [
      /\/properties\/BPVL\d+$/i,
      /\/buy\/properties\/[^\/]+$/i
    ];
    
    return propertyPatterns.some(pattern => pattern.test(url));
  }

  // Discover URLs from all sitemaps for a website
  async discoverUrlsFromSitemaps(websiteId) {
    console.log(`🚀 Discovering URLs from sitemaps for ${websiteId}...`);
    
    const sitemapUrls = this.sitemapUrls[websiteId] || [];
    if (sitemapUrls.length === 0) {
      console.log(`⚠️  No sitemaps configured for ${websiteId}`);
      return { propertyUrls: [], listingUrls: [] };
    }

    let allUrls = [];
    
    for (const sitemapUrl of sitemapUrls) {
      const sitemapData = await this.fetchSitemap(sitemapUrl);
      if (sitemapData) {
        const urls = this.extractUrlsFromSitemap(sitemapData);
        allUrls = allUrls.concat(urls);
        
        // If this is a sitemap index, fetch child sitemaps
        const childSitemaps = urls.filter(u => u.type === 'sitemap');
        for (const childSitemap of childSitemaps) {
          console.log(`🔍 Fetching child sitemap: ${childSitemap.url}`);
          const childData = await this.fetchSitemap(childSitemap.url);
          if (childData) {
            const childUrls = this.extractUrlsFromSitemap(childData);
            allUrls = allUrls.concat(childUrls);
          }
        }
      }
      
      // Wait between requests to be polite
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log(`📊 Total URLs found in sitemaps: ${allUrls.length}`);
    
    // Filter for property URLs
    const filtered = this.filterPropertyUrls(allUrls, websiteId);
    
    return filtered;
  }

  // Check if URL already exists in discovered_urls table
  async urlExists(url) {
    try {
      const existing = await db.select()
        .from(discoveredUrls)
        .where(eq(discoveredUrls.url, url))
        .limit(1);
      
      return existing.length > 0;
    } catch (error) {
      console.error(`❌ Error checking URL existence: ${error.message}`);
      return false;
    }
  }

  // Add discovered URLs to the system using modern queue manager approach
  async addUrlsToQueue(propertyUrls, priority = 8) {
    console.log(`➕ Adding ${propertyUrls.length} property URLs to queue...`);
    
    let added = 0;
    let skipped = 0;
    
    for (const urlData of propertyUrls) {
      try {
        // Check if URL already exists
        const exists = await this.urlExists(urlData.url);
        if (exists) {
          skipped++;
          continue;
        }

        // Create discovered URL entry
        const discoveredUrl = await db.insert(discoveredUrls).values({
          url: urlData.url,
          website_id: urlData.website_id,
          url_type: 'property',
          is_property_page: 1,
          confidence_score: urlData.confidence || 0.9,
          classification_reason: 'Sitemap discovery',
          discovered_at: new Date()
        }).returning();

        // Add to scraping queue
        await db.insert(scrapingQueue).values({
          discovered_url_id: discoveredUrl[0].id,
          url: urlData.url,
          website_id: urlData.website_id,
          priority: priority,
          scheduled_for: new Date(),
          attempts: 0,
          max_attempts: 3,
          status: 'pending',
          created_at: new Date(),
          updated_at: new Date()
        });
        
        added++;
        if (added <= 10) { // Show first 10
          console.log(`✅ ${added}: ${urlData.url.substring(0, 80)}...`);
        }
        
      } catch (error) {
        console.log(`❌ Error adding URL ${urlData.url}: ${error.message}`);
        skipped++;
      }
    }
    
    console.log(`\n📊 Results:`);
    console.log(`   ✅ Added: ${added} URLs`);
    console.log(`   ⚠️  Skipped (duplicates/errors): ${skipped} URLs`);
    
    return { added, skipped };
  }
}

module.exports = { SitemapDiscovery };
