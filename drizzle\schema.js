// Database Schema for Real Estate Agent
const { 
  pgTable, 
  uuid, 
  varchar, 
  text, 
  decimal, 
  integer, 
  timestamp, 
  jsonb, 
  pgEnum,
  unique,
  vector
} = require('drizzle-orm/pg-core');
const { sql } = require('drizzle-orm');

// Enums
const propertyCategory = pgEnum('EPropertyCategory', ['RESIDENTIAL', 'COMMERCIAL', 'LAND']);
const propertyType = pgEnum('EPropertyType', ['VILLA', 'HOUSE', 'APARTMENT', 'TOWNHOUSE', 'LAND', 'COMMERCIAL']);
const propertyStatus = pgEnum('EPropertyStatus', ['AVAILABLE', 'SOLD', 'RENTED', 'PENDING']);

// Properties table
const properties = pgTable('property', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  
  // Basic property information
  title: varchar('title', { length: 500 }).notNull(),
  category: propertyCategory('category').notNull(),
  type: propertyType('type').notNull(),
  status: propertyStatus('status').notNull().default('AVAILABLE'),
  
  // Location information
  address: text('address'),
  city: varchar('city', { length: 100 }),
  state: varchar('state', { length: 100 }),
  country: varchar('country', { length: 100 }).default('Indonesia'),
  
  // Pricing information
  price: decimal('price', { precision: 15, scale: 2 }), // Sale price in IDR
  rent_price: decimal('rent_price', { precision: 15, scale: 2 }), // Monthly rent in IDR
  
  // Property specifications
  bedrooms: integer('bedrooms'),
  bathrooms: integer('bathrooms'),
  parking_spaces: integer('parking_spaces'),
  size_sqft: decimal('size_sqft', { precision: 10, scale: 2 }),
  lot_size_sqft: decimal('lot_size_sqft', { precision: 10, scale: 2 }),
  year_built: integer('year_built'),
  
  // JSON fields for flexible data
  amenities: jsonb('amenities'), // Structured amenities data
  media: jsonb('media'), // Images, videos, brochures
  
  // Source tracking
  source_id: varchar('source_id', { length: 100 }).notNull(),
  external_id: varchar('external_id', { length: 200 }),
  source_url: text('source_url'),
  
  // Vector for semantic search (1536 dimensions for OpenAI embeddings)
  vector: vector('vector', { dimensions: 1536 }),
  
  // Timestamps
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
  scraped_at: timestamp('scraped_at'),
}, (table) => ({
  // Unique constraint for preventing duplicates from same source
  sourceExternalUnique: unique().on(table.source_id, table.external_id),
}));

// Exchange rates table
const exchangeRates = pgTable('exchange_rates', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  from_currency: varchar('from_currency', { length: 3 }).notNull(),
  to_currency: varchar('to_currency', { length: 3 }).notNull(),
  rate: decimal('rate', { precision: 15, scale: 6 }).notNull(),
  date: timestamp('date').notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Sources table for tracking data sources
const sources = pgTable('sources', {
  id: varchar('id', { length: 100 }).primaryKey(),
  name: varchar('name', { length: 200 }).notNull(),
  base_url: text('base_url'),
  description: text('description'),
  is_active: integer('is_active').default(1), // Using integer for boolean (1/0)
  last_scraped: timestamp('last_scraped'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Scraping queue table
const scrapingQueue = pgTable('scraping_queue', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  discovered_url_id: uuid('discovered_url_id'),
  url: varchar('url').notNull(),
  website_id: varchar('website_id').notNull(),
  priority: integer('priority'),
  scheduled_for: timestamp('scheduled_for'),
  attempts: integer('attempts'),
  max_attempts: integer('max_attempts'),
  status: varchar('status'),
  assigned_to: varchar('assigned_to'),
  started_at: timestamp('started_at'),
  completed_at: timestamp('completed_at'),
  error_message: text('error_message'),
  created_at: timestamp('created_at'),
  updated_at: timestamp('updated_at'),
});

// Discovered URLs table
const discoveredUrls = pgTable('discovered_urls', {
  id: uuid('id').primaryKey().default(sql`gen_random_uuid()`),
  url: text('url').notNull(),
  website_id: varchar('website_id', { length: 100 }).notNull(),
  url_type: varchar('url_type', { length: 50 }),
  is_property_page: integer('is_property_page').default(0), // Using integer for boolean
  confidence_score: decimal('confidence_score', { precision: 3, scale: 2 }),
  classification_reason: text('classification_reason'),
  discovered_at: timestamp('discovered_at').defaultNow().notNull(),
});

module.exports = {
  properties,
  exchangeRates,
  sources,
  scrapingQueue,
  discoveredUrls,
  propertyCategory,
  propertyType,
  propertyStatus
};
