// taken from ry's node_postgres module;
;
exports.BOOL = 16;
exports.BYTEA = 17;
exports.CHAR = 18;
exports.NAME = 19;
exports.INT8 = 20;
exports.INT2 = 21;
exports.INT2VECTOR = 22;
exports.INT4 = 23;
exports.REGPROC = 24;
exports.TEXT = 25;
exports.OID = 26;
exports.TID = 27;
exports.XID = 28;
exports.CID = 29;
exports.VECTOROID = 30;
exports.PG_TYPE_RELTYPE_ = 71;
exports.PG_ATTRIBUTE_RELTYPE_ = 75;
exports.PG_PROC_RELTYPE_ = 81;
exports.PG_CLASS_RELTYPE_ = 83;
exports.POINT = 600;
exports.LSEG = 601;
exports.PATH = 602;
exports.BOX = 603;
exports.POLYGON = 604;
exports.LINE = 628;
exports.FLOAT4 = 700;
exports.FLOAT8 = 701;
exports.ABSTIME = 702;
exports.RELTIME = 703;
exports.TINTERVAL = 704;
exports.UNKNOWN = 705;
exports.CIRCLE = 718;
exports.CASH = 790;
exports.MACADDR = 829;
exports.INET = 869;
exports.CIDR = 650;
exports.INT4ARRAY = 1007;
exports.ACLITEM = 1033;
exports.BPCHAR = 1042;
exports.VARCHAR = 1043;
exports.DATE = 1082;
exports.TIME = 1083;
exports.TIMESTAMP = 1114;
exports.TIMESTAMPTZ = 1184;
exports.INTERVAL = 1186;
exports.TIMETZ = 1266;
exports.BIT = 1560;
exports.VARBIT = 1562;
exports.NUMERIC = 1700;
exports.REFCURSOR = 1790;
exports.REGPROCEDURE = 2202;
exports.REGOPER = 2203;
exports.REGOPERATOR = 2204;
exports.REGCLASS = 2205;
exports.REGTYPE = 2206;
exports.RECORD = 2249;
exports.CSTRING = 2275;
exports.ANY = 2276;
exports.ANYARRAY = 2277;
exports.VOID = 2278;
exports.TRIGGER = 2279;
exports.LANGUAGE_HANDLER = 2280;
exports.INTERNAL = 2281;
exports.OPAQUE = 2282;
exports.ANYELEMENT = 2283;
